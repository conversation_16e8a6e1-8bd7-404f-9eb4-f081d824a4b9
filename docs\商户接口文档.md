# CRMEB商户接口文档

## 1. 商户分页列表

获取商户的分页列表数据。

### 请求URL

```
GET /api/admin/platform/merchant/list
```

### 权限要求

需要 `platform:merchant:page:list` 权限。

### 请求参数

#### 查询参数

| 参数名 | 类型 | 是否必须 | 说明 |
| --- | --- | --- | --- |
| categoryId | Integer | 否 | 商户分类ID |
| typeId | Integer | 否 | 商户类型ID |
| phone | String | 否 | 商户手机号 |
| keywords | String | 否 | 商户关键字：支持店铺名称、关键字 |
| isSelf | Boolean | 否 | 是否自营：0-非自营，1-自营 |
| isSwitch | Boolean | 否 | 商户开关:0-关闭，1-开启 |
| dateLimit | String | 否 | 创建时间区间 |

#### 分页参数

| 参数名 | 类型 | 是否必须 | 说明 | 默认值 |
| --- | --- | --- | --- | --- |
| page | Integer | 否 | 页码 | 1 |
| limit | Integer | 否 | 每页数量 | 20 |

### 响应参数

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 提示信息 |
| data | Object | 响应数据 |
| └─ total | Integer | 总记录数 |
| └─ list | Array | 商户列表 |
| &nbsp;&nbsp;&nbsp;└─ id | Integer | 商户ID |
| &nbsp;&nbsp;&nbsp;└─ name | String | 商户名称 |
| &nbsp;&nbsp;&nbsp;└─ categoryId | Integer | 商户分类ID |
| &nbsp;&nbsp;&nbsp;└─ typeId | Integer | 商户类型ID |
| &nbsp;&nbsp;&nbsp;└─ realName | String | 商户姓名 |
| &nbsp;&nbsp;&nbsp;└─ phone | String | 商户手机号 |
| &nbsp;&nbsp;&nbsp;└─ isSelf | Boolean | 是否自营：0-非自营，1-自营 |
| &nbsp;&nbsp;&nbsp;└─ isRecommend | Boolean | 是否推荐:0-不推荐，1-推荐 |
| &nbsp;&nbsp;&nbsp;└─ isSwitch | Boolean | 商户开关:0-关闭，1-开启 |
| &nbsp;&nbsp;&nbsp;└─ copyProductNum | Integer | 复制商品数量 |
| &nbsp;&nbsp;&nbsp;└─ starLevel | Integer | 商户星级1-5 |
| &nbsp;&nbsp;&nbsp;└─ remark | String | 备注 |
| &nbsp;&nbsp;&nbsp;└─ rectangleLogo | String | 商户logo（横） |
| &nbsp;&nbsp;&nbsp;└─ coverImage | String | 商户封面图 |
| &nbsp;&nbsp;&nbsp;└─ sort | Integer | 排序 |
| &nbsp;&nbsp;&nbsp;└─ createType | String | 商户创建类型：admin-管理员创建，apply-商户入驻申请 |
| &nbsp;&nbsp;&nbsp;└─ createName | String | 商户创建管理员名称 |
| &nbsp;&nbsp;&nbsp;└─ createTime | Date | 创建时间 |
| &nbsp;&nbsp;&nbsp;└─ appId | String | app_id |
| &nbsp;&nbsp;&nbsp;└─ appSecret | String | API秘钥 |
| &nbsp;&nbsp;&nbsp;└─ merchantCallbackUrl | String | 回调地址 |
| &nbsp;&nbsp;&nbsp;└─ merchantRequestWhiteIps | String | 商户请求ip白名单 |

### 响应示例

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "total": 100,
    "list": [
      {
        "id": 1,
        "name": "测试商户",
        "categoryId": 1,
        "typeId": 1,
        "realName": "张三",
        "phone": "13800138000",
        "isSelf": true,
        "isRecommend": true,
        "isSwitch": true,
        "copyProductNum": 100,
        "starLevel": 5,
        "remark": "备注信息",
        "rectangleLogo": "http://example.com/logo.png",
        "coverImage": "http://example.com/cover.png",
        "sort": 0,
        "createType": "admin",
        "createName": "管理员",
        "createTime": "2023-01-01 00:00:00",
        "appId": "app123456",
        "appSecret": "sec123456",
        "merchantCallbackUrl": "http://example.com/callback",
        "merchantRequestWhiteIps": "***********,***********"
      }
    ]
  }
}
```

## 2. 添加商户

添加新的商户信息。

### 请求URL

```
POST /api/admin/platform/merchant/add
```

### 权限要求

需要 `platform:merchant:add` 权限。

### 请求参数

| 参数名 | 类型 | 是否必须 | 说明 |
| --- | --- | --- | --- |
| name | String | 是 | 商户名称 (不超过16个字符) |
| categoryId | Integer | 是 | 商户分类ID |
| typeId | Integer | 是 | 商户类型ID |
| realName | String | 是 | 商户姓名 |
| phone | String | 是 | 商户手机号 (符合手机号格式) |
| handlingFee | Integer | 是 | 手续费(%), 范围0-100 |
| keywords | String | 否 | 商户关键字 |
| isSelf | Boolean | 是 | 是否自营：0-非自营，1-自营 |
| isRecommend | Boolean | 是 | 是否推荐:0-不推荐，1-推荐 |
| isSwitch | Boolean | 是 | 商户开关:0-关闭，1-开启 |
| productSwitch | Boolean | 是 | 商品审核开关:0-关闭，1-开启 |
| qualificationPicture | String | 是 | 资质图片 |
| remark | String | 否 | 备注 |
| sort | Integer | 是 | 排序 (范围0-9999) |
| appId | String | 否 | app_id |
| appSecret | String | 否 | API秘钥 |
| merchantCallbackUrl | String | 否 | 回调地址 |
| merchantRequestWhiteIps | String | 否 | 商户请求ip白名单 |

### 请求示例

```json
{
  "name": "测试商户",
  "categoryId": 1,
  "typeId": 1,
  "realName": "张三",
  "phone": "13800138000",
  "handlingFee": 5,
  "keywords": "测试,商户",
  "isSelf": true,
  "isRecommend": true,
  "isSwitch": true,
  "productSwitch": true,
  "qualificationPicture": "http://example.com/qualification.png",
  "remark": "备注信息",
  "sort": 0,
  "appId": "app123456",
  "appSecret": "sec123456",
  "merchantCallbackUrl": "http://example.com/callback",
  "merchantRequestWhiteIps": "***********,***********"
}
```

### 响应参数

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 提示信息 |
| data | String | 响应数据 |

### 响应示例

```json
{
  "code": 200,
  "msg": "success",
  "data": "添加商户成功"
}
```

## 3. 编辑商户

编辑已有的商户信息。

### 请求URL

```
POST /api/admin/platform/merchant/update
```

### 权限要求

需要 `platform:merchant:update` 权限。

### 请求参数

| 参数名 | 类型 | 是否必须 | 说明 |
| --- | --- | --- | --- |
| id | Integer | 是 | 商户ID |
| name | String | 是 | 商户名称 (不超过50个字符) |
| categoryId | Integer | 是 | 商户分类ID |
| typeId | Integer | 是 | 商户类型ID |
| realName | String | 否 | 商户姓名 |
| handlingFee | Integer | 是 | 手续费(%), 范围0-100 |
| keywords | String | 否 | 商户关键字 |
| addressDetail | String | 否 | 商户详细地址 |
| latitude | String | 否 | 纬度 |
| longitude | String | 否 | 经度 |
| isSelf | Boolean | 是 | 是否自营：0-非自营，1-自营 |
| isRecommend | Boolean | 是 | 是否推荐:0-不推荐，1-推荐 |
| productSwitch | Boolean | 是 | 商品审核开关:0-关闭，1-开启 |
| starLevel | Integer | 是 | 商户星级1-5 |
| remark | String | 否 | 备注 |
| sort | Integer | 是 | 排序 (范围0-9999) |
| qualificationPicture | String | 否 | 资质图片 |
| appId | String | 否 | app_id |
| appSecret | String | 否 | API秘钥 |
| merchantCallbackUrl | String | 否 | 回调地址 |
| merchantRequestWhiteIps | String | 否 | 商户请求ip白名单 |

### 请求示例

```json
{
  "id": 1,
  "name": "测试商户",
  "categoryId": 1,
  "typeId": 1,
  "realName": "张三",
  "handlingFee": 5,
  "keywords": "测试,商户",
  "addressDetail": "广东省深圳市南山区",
  "latitude": "22.5431",
  "longitude": "114.0579",
  "isSelf": true,
  "isRecommend": true,
  "productSwitch": true,
  "starLevel": 5,
  "remark": "备注信息",
  "sort": 0,
  "qualificationPicture": "http://example.com/qualification.png",
  "appId": "app123456",
  "appSecret": "sec123456",
  "merchantCallbackUrl": "http://example.com/callback",
  "merchantRequestWhiteIps": "***********,***********"
}
```

### 响应参数

| 参数名 | 类型 | 说明 |
| --- | --- | --- |
| code | Integer | 状态码，200表示成功 |
| msg | String | 提示信息 |
| data | String | 响应数据 |

### 响应示例

```json
{
  "code": 200,
  "msg": "success",
  "data": "编辑商户成功"
}
``` 