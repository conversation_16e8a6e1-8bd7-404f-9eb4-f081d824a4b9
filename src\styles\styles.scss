@charset "UTF-8";

.el-alert__icon.is-big {
  font-size: 16px !important;
}

.el-icon-error {
  color: #ccc;
}

//diy缺省图标
.icon-tu {
  color: #c7ccd1;
  font-size: 50px !important;
}

.line-height-15 {
  line-height: 1.5;
}

.line-heightOne {
  line-height: 1;
}

.textE93323 {
  color: #e93323 !important;
}

.demo-drawer_title {
  .info {
    font-size: 13px;
    font-family: PingFang SC-Regular, PingFang SC;
    font-weight: 400;
    color: #606266;
    margin-top: 10px;
  }
}

#app .sidebar-container .is-active {
  background: none !important;
}

//登录页动画
.index_bg {
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6) !important;
  z-index: 0 !important;
}

.divBox {
  box-sizing: border-box;

  .el-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 25px;
    padding: 0;
  }
}

/* 去掉滚动条 */
.scrollbarAll::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}

.scrollbarAll {
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
  overflow-x: hidden;
  overflow-y: auto;
}

.seachTiele {
  font-size: 12px;
  line-height: 29px;
}

.seachWidth {
  //width: 219px !important;
}

.el-divider--horizontal {
  margin: 19px 0;
}

//.el-dialog__footer {
//  border-top: 1px solid #dcdfe6;
//}

.el-message-box__wrapper {
  overflow: auto;
}

.modal-form {
  width: 700px;
}

table .el-image,
table img {
  width: 36px !important;
  height: 36px !important;
  border-radius: 4px;
}

.upload-form {
  min-width: 540px;
  max-height: 620px;
}

.upload-form-temp {
  min-width: 1000px;
  max-height: 700px;
}

.listPic {
  .image-slot {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.switchTable {
  .el-switch.is-disabled {
    opacity: 1;
  }

  .el-switch.is-disabled .el-switch__core,
  .el-switch.is-disabled .el-switch__label {
    cursor: pointer !important;
  }
}

/**
 * 上传图片的照相机
 */
.upLoadPicBox {
  display: inline-block;
  cursor: pointer;

  .upLoad {
    width: 58px;
    height: 58px;
    line-height: 58px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.02);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.cameraIconfont {
  color: #898989;
  font-size: 26px;
}

//表格图片宽度
.tabBox_img {
  width: 36px;
  height: 36px;
  border-radius: 4px;
  display: table-cell;
  vertical-align: middle;
}

.tabBox_img img {
  width: 100%;
  height: 100%;
}

.picMiddle {
  display: table;
}

.spBlock {
  display: block;
}

//表格头部颜色
.el-table thead {
  color: #333 !important;
}

// 模态框
.creatformModel {
  min-width: 700px;
  max-height: 620px;
}

.width100 {
  width: 100%;
}

//点击上传图样式（弹窗）
.publicPicBox {
  display: inline-block;
  cursor: pointer;
}

.publicPicBox .upLoad {
  width: 58px;
  height: 58px;
  line-height: 58px;
  border: 1px dotted rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background: rgba(0, 0, 0, 0.02);
}

.publicPicBox .pictrue {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px dotted rgba(0, 0, 0, 0.1);
  margin-right: 10px;
}

.publicPicBox .pictrue img {
  width: 100%;
  height: 100%;
}

.publicPicBox .iconfont {
  color: #898989;
  font-size: 18px;
}

//全局弹窗宽度；
.dialogWidth {
  width: 80%;
}

/**
 * 表格下拉内容
 */
.demo-table-expand {
  font-size: 0;
}

.demo-table-expand label {
  width: 111px;
}

.demo-table-expand .el-form-item {
  margin-right: 0;
  margin-bottom: 0;
  width: 33.33%;
}

.selWidth {
  width: 260px !important;
}

//富文本编辑器弹框
#edui_fixedlayer {
  z-index: 4000 !important;
}

.edui-dialog {
  z-index: 4009 !important;
}

.maskModel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 55;
  background-color: rgba(0, 0, 0, 0.5);
}

.line2 {
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.el-image-viewer__close {
  color: #fff;
}

.el-message-box__content {
  padding: 30px 24px 0 24px;
}

// 自定义表单 加载的弹窗样式 END

//抽屉提交按钮
.from-foot-btn {
  width: 100%;
  padding: 20px;
  background: #fff;
}

.witth50 {
  width: 50%;
}

.drawer_fix {
  z-index: 10;
  position: absolute;
  left: 0;
  bottom: 0px;
  padding-bottom: 10px;
  background: #fff;
}

.fix {
  position: fixed;
  bottom: 0;
}

.btn-shadow {
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05);
}

//多图中图片样式
.pictrue {
  width: 60px;
  height: 60px;
  border: 1px dotted rgba(0, 0, 0, 0.1);
  margin-right: 10px;
  position: relative;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
  }

  video {
    width: 100%;
    height: 100%;
  }
}

//多图中删除图片按钮
.btndel {
  position: absolute;
  z-index: 1;
  width: 20px !important;
  height: 20px !important;
  left: 46px;
  top: -4px;
}

/*抽屉slot头部标题*/
.demo-drawer_title {
  font-size: 19px;
  color: #303133;
}

/***************diy样式*****************/
/**
 * dyi首页模块中标题样式
 */
.indexList {
  background-color: #fff;
  border-radius: 6px;
  padding: 15px 12px;

  .title {
    .text {
      width: 260px;
      color: #999999;
      font-size: 12px;
      display: flex;
      align-items: flex-end;

      .image {
        width: 62px;
        height: 16px;
      }

      .label {
        font-size: 11px;
        color: #999999;
        margin-left: 5px;
        position: relative;
        top: 1px;
      }
    }

    .more {
      font-size: 8px;
      padding: 2px 5px;
      text-align: center;
      border-radius: 4px;

      .iconfont {
        font-size: 9px;
      }
    }
  }

  .tips {
    color: rgba(51, 51, 51, 0.3);
    font-size: 9px;
  }

  .list {
    width: 100%;
    border-radius: 7px;
    background-color: #fff;
    box-sizing: border-box;
    margin-top: 15px;

    .item {
      width: 100px;
      background: #fff;
      margin-right: 10px;
    }

    .item:nth-last-child(1) {
      margin-right: 0;
    }
  }
}
.labelwidth {
  width: 72px;
}
.labelml {
  margin-left: 22px;
}
.mobile-config {
  width: 100%;
  padding: 15px;
}
.borderPadding {
  padding: 0 15px;
}
.c_label {
  font-size: 12px;
  color: #999;
}
.c_label .labelwidth {
  color: #999;
  display: inline-block;
}
.slider-box {
  width: 81% !important;
}
//右上角标题
.title-config-diy {
  height: 63px;
  padding: 0 15px;
  line-height: 63px;
  .title-bar {
    font-size: 16px;
    color: #333;
    font-family: PingFang SC-Medium, PingFang SC;
    font-weight: 600;
  }
}
//系统表单样式
.mobile-config-from {
  border-top: 6px solid #f6f7f9;
  padding-top: 20px;
  .labelwidth {
    //text-align: right;
  }
}
/***************diy样式*****************/

//带蓝色横杠标题
.title-bar-line {
  width: 100%;
  height: 38px;
  line-height: 38px;
  padding-left: 10px;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 10px;
    width: 2px;
    height: 16px;
    background: var(--prev-color-primary);
  }
}

.empty-box {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e6e9ed;
  border-radius: 6px;
}

.empty-box.on {
  border-radius: 0px;
}

.empty-box .iconfont-diy {
  color: #bbbfc8;
  font-size: 30px;
}

.c_row-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
//退款详情页面样式
.detail {
  &-section {
    padding: 0 25px 15px 25px;
  }

  &-title {
    padding-left: 10px;
    border-left: 3px solid var(--prev-color-primary);
    font-weight: 500;
    font-size: 15px;
    line-height: 16px;
    color: #303133;
    margin-bottom: 15px;
  }

  &-term {
    font-size: 12px;
    color: #666666;
    margin-bottom: 10px;
  }

  &-info {
    font-size: 12px;
    color: #333333;
  }

  &-infoTitle {
    width: 85px;
    display: inline-block;
    text-align: right;
  }
}

/********************详情页样式****************************/
.detailHead {
  padding: 0 24px 24px 35px;

  .headerBox {
    align-items: flex-start;
  }

  .full {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .order_icon {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      background-color: var(--prev-color-primary);
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .iconfont {
      color: #fff;
      font-size: 35px;

      &.sale-after {
        color: #90add5;
      }
    }

    .text {
      width: 682px;
      align-self: center;
      flex: 1;
      min-width: 0;
      padding-left: 12px;
      font-size: 13px;
      color: #606266;

      .title {
        margin-bottom: 10px;
        font-weight: 500;
        font-size: 16px;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.85);
      }

      .order-num {
        padding-top: 10px;
        white-space: nowrap;
      }
    }
  }

  .list {
    display: flex;
    margin-top: 20px;
    overflow: hidden;
    list-style: none;
    padding: 0;

    .item {
      flex: none;
      width: 200px;
      font-size: 14px;
      line-height: 14px;
      color: rgba(0, 0, 0, 0.85);

      .title {
        margin-bottom: 12px;
        font-size: 13px;
        line-height: 13px;
        color: #666666;
      }

      .value1 {
        color: #f56022;
      }

      .value2 {
        color: #1bbe6b;
      }

      .value3 {
        color: var(--prev-color-primary);
      }

      .value4 {
        color: #6a7b9d;
      }

      .value5 {
        color: #f5222d;
      }
    }
  }
}
.detailSection {
  ::v-deep .el-table__row {
    padding-left: 15px !important;
  }

  padding: 25px 0;
  border-top: 1px dashed #eeeeee;

  .mt-16 {
    margin-top: -16px;
  }

  .title {
    padding-left: 10px;
    border-left: 3px solid var(--prev-color-primary);
    font-size: 15px;
    line-height: 15px;
    color: #303133;
  }

  .list {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
  }

  .lang {
    width: 100px;
    text-align: right;
  }

  .tips {
    width: 78px;
    text-align: right;
    font-size: 13px;
    color: #606266;
  }

  .item {
    flex: 0 0 calc(100% / 3);
    display: flex;
    margin-top: 16px;
    font-size: 13px;
    color: #606266;

    &:nth-child(3n + 1) {
      padding-right: 20px;
    }

    &:nth-child(3n + 2) {
      padding-right: 20px;
      //  padding-left: 10px;
    }

    &:nth-child(3n + 3) {
      //  padding-left: 20px;
    }
  }

  .value {
    flex: 1;
    color: #303133;

    image {
      display: inline-block;
      width: 40px;
      height: 40px;
      margin: 0 12px 12px 0;
      vertical-align: middle;
    }
  }

  .item.pic {
    display: flex;

    img {
      width: 80px;
      height: 80px;
    }
  }
}
.padBox {
  padding: 25px 35px !important;
}
//表单内输入框宽度
.from-ipt-width {
  width: 460px;
}
/*****************************/

/******************** 弹窗 from 样式重写 start ****************************/

.from-tips {
  font-size: 12px;
  color: #909399;
  line-height: 12px;
  text-align: left;
  margin-top: 14px;
}

//登录显示隐藏按钮
$dark_gray: #889aa4;
.show-pwd {
  position: absolute;
  right: 10px;
  top: 2px;
  font-size: 16px;
  color: $dark_gray;
  cursor: pointer;
  user-select: none;
  ::v-deepsvg-icon {
    vertical-align: 0.3em;
  }
}

// 上一步按钮样式 次要按钮
.priamry_border {
  border: 1px solid var(--prev-color-primary);
  color: var(--prev-color-primary);
}

//带竖线的标题
.header_title {
  font-size: 15px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 500;
  color: #303133;
  padding-left: 8px;
  position: relative;
  margin-left: 20px;
  &::before {
    position: absolute;
    content: '';
    width: 3px;
    height: 18px;
    background: var(--prev-color-primary);
    top: 0;
    left: 0;
  }
}

// 上传图片Dialog
.uploadDialog {
  .el-dialog__body {
    max-height: none !important;
  }
}
